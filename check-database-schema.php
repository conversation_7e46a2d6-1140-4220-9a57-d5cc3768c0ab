<?php
/**
 * Check database schema for DCIM tables
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include WHMCS bootstrap
require_once '../../../init.php';

use WHMCS\Database\Capsule;

echo '<h1>DCIM Database Schema Check</h1>';

try {
    // Check if dcim_subnets table exists and get its structure
    echo '<h2>dcim_subnets Table Structure</h2>';
    
    if (Capsule::schema()->hasTable('dcim_subnets')) {
        echo '<p>✅ Table exists</p>';
        
        // Get table structure
        $columns = Capsule::select("DESCRIBE dcim_subnets");
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
        foreach ($columns as $column) {
            echo '<tr>';
            echo '<td>' . $column->Field . '</td>';
            echo '<td>' . $column->Type . '</td>';
            echo '<td>' . $column->Null . '</td>';
            echo '<td>' . $column->Key . '</td>';
            echo '<td>' . ($column->Default ?? 'NULL') . '</td>';
            echo '<td>' . $column->Extra . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        
        // Check for specific columns
        $required_columns = ['id', 'subnet', 'network', 'prefix_length', 'status', 'location_id'];
        echo '<h3>Required Columns Check</h3>';
        foreach ($required_columns as $col) {
            $exists = Capsule::schema()->hasColumn('dcim_subnets', $col);
            echo '<p>' . $col . ': ' . ($exists ? '✅ EXISTS' : '❌ MISSING') . '</p>';
        }
        
        // Check for parent_id column (for hierarchy)
        $hasParentId = Capsule::schema()->hasColumn('dcim_subnets', 'parent_id');
        echo '<p>parent_id (hierarchy): ' . ($hasParentId ? '✅ EXISTS' : '❌ MISSING') . '</p>';
        
        // Get sample data
        echo '<h3>Sample Data</h3>';
        $sample_data = Capsule::table('dcim_subnets')->limit(5)->get();
        if (count($sample_data) > 0) {
            echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
            $first_row = true;
            foreach ($sample_data as $row) {
                if ($first_row) {
                    echo '<tr>';
                    foreach ($row as $key => $value) {
                        echo '<th>' . htmlspecialchars($key) . '</th>';
                    }
                    echo '</tr>';
                    $first_row = false;
                }
                echo '<tr>';
                foreach ($row as $key => $value) {
                    echo '<td>' . htmlspecialchars($value ?? 'NULL') . '</td>';
                }
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<p>No data found</p>';
        }
        
    } else {
        echo '<p>❌ Table does not exist</p>';
    }
    
    // Check other related tables
    echo '<h2>Related Tables Check</h2>';
    $related_tables = ['dcim_locations', 'dcim_ip_addresses', 'dcim_ip_assignments'];
    foreach ($related_tables as $table) {
        $exists = Capsule::schema()->hasTable($table);
        echo '<p>' . $table . ': ' . ($exists ? '✅ EXISTS' : '❌ MISSING') . '</p>';
        
        if ($exists) {
            $count = Capsule::table($table)->count();
            echo '<p>&nbsp;&nbsp;&nbsp;Records: ' . $count . '</p>';
        }
    }
    
    // Check for any data integrity issues
    echo '<h2>Data Integrity Check</h2>';
    
    if (Capsule::schema()->hasTable('dcim_subnets')) {
        // Check for NULL subnet values
        $null_subnets = Capsule::table('dcim_subnets')->whereNull('subnet')->count();
        echo '<p>Subnets with NULL subnet field: ' . $null_subnets . '</p>';
        
        // Check for invalid CIDR format
        $subnets = Capsule::table('dcim_subnets')->get();
        $invalid_cidrs = 0;
        foreach ($subnets as $subnet) {
            if (!empty($subnet->subnet) && !preg_match('/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/', $subnet->subnet)) {
                $invalid_cidrs++;
                echo '<p>Invalid CIDR format: ' . htmlspecialchars($subnet->subnet) . ' (ID: ' . $subnet->id . ')</p>';
            }
        }
        echo '<p>Total invalid CIDR formats: ' . $invalid_cidrs . '</p>';
        
        // Check for orphaned records
        if (Capsule::schema()->hasTable('dcim_locations')) {
            $orphaned_subnets = Capsule::table('dcim_subnets')
                ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
                ->whereNotNull('dcim_subnets.location_id')
                ->whereNull('dcim_locations.id')
                ->count();
            echo '<p>Orphaned subnets (invalid location_id): ' . $orphaned_subnets . '</p>';
        }
    }
    
} catch (Exception $e) {
    echo '<h2>❌ Error</h2>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

?>
