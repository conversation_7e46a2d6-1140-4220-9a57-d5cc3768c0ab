<?php
/**
 * Debug script for subnet modal JSON parsing error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include WHMCS bootstrap
require_once '../../../init.php';

use WHMCS\Database\Capsule;

// Include DCIM files
require_once 'dcim-core.php';
require_once 'dcim-ipam.php';

echo '<h1>Debug Subnet Modal JSON Error</h1>';

try {
    // Check if tables exist
    echo '<h2>Database Table Check</h2>';
    $tables = ['dcim_subnets', 'dcim_locations', 'dcim_ip_addresses', 'dcim_ip_assignments'];
    foreach ($tables as $table) {
        $exists = Capsule::schema()->hasTable($table);
        echo '<p>' . $table . ': ' . ($exists ? '✅ EXISTS' : '❌ MISSING') . '</p>';
        
        if ($exists && $table === 'dcim_subnets') {
            // Check columns
            $columns = Capsule::schema()->getColumnListing($table);
            echo '<p>Columns: ' . implode(', ', $columns) . '</p>';
            
            // Check if parent_id column exists
            $hasParentId = Capsule::schema()->hasColumn($table, 'parent_id');
            echo '<p>Has parent_id column: ' . ($hasParentId ? '✅ YES' : '❌ NO') . '</p>';
        }
    }
    
    // Get existing subnets
    echo '<h2>Existing Subnets</h2>';
    $subnets = Capsule::table('dcim_subnets')->get();
    echo '<p>Found ' . count($subnets) . ' subnets</p>';
    
    if (count($subnets) > 0) {
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>ID</th><th>Subnet</th><th>Network</th><th>Prefix</th><th>Status</th><th>Location ID</th></tr>';
        foreach ($subnets as $subnet) {
            echo '<tr>';
            echo '<td>' . $subnet->id . '</td>';
            echo '<td>' . htmlspecialchars($subnet->subnet ?? 'NULL') . '</td>';
            echo '<td>' . htmlspecialchars($subnet->network ?? 'NULL') . '</td>';
            echo '<td>' . htmlspecialchars($subnet->prefix_length ?? 'NULL') . '</td>';
            echo '<td>' . htmlspecialchars($subnet->status ?? 'NULL') . '</td>';
            echo '<td>' . htmlspecialchars($subnet->location_id ?? 'NULL') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        
        // Test the AJAX function with the first subnet
        $test_subnet = $subnets[0];
        echo '<h2>Testing AJAX Function for Subnet ID: ' . $test_subnet->id . '</h2>';
        
        // Test each component separately
        echo '<h3>1. Testing Subnet Query</h3>';
        try {
            $subnet = Capsule::table('dcim_subnets')
                ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
                ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
                ->where('dcim_subnets.id', $test_subnet->id)
                ->first();
            
            if ($subnet) {
                echo '<p>✅ Subnet query successful</p>';
                echo '<pre>' . print_r($subnet, true) . '</pre>';
            } else {
                echo '<p>❌ Subnet not found</p>';
            }
        } catch (Exception $e) {
            echo '<p>❌ Subnet query error: ' . $e->getMessage() . '</p>';
        }
        
        echo '<h3>2. Testing Status Function</h3>';
        try {
            $status = dcim_get_subnet_status($test_subnet->id);
            echo '<p>✅ Status function successful</p>';
            echo '<pre>' . print_r($status, true) . '</pre>';
        } catch (Exception $e) {
            echo '<p>❌ Status function error: ' . $e->getMessage() . '</p>';
        }
        
        echo '<h3>3. Testing Children Query</h3>';
        try {
            $children = [];
            if (Capsule::schema()->hasColumn('dcim_subnets', 'parent_id')) {
                $children = Capsule::table('dcim_subnets')
                    ->where('parent_id', $test_subnet->id)
                    ->orderBy('network')
                    ->get();
                echo '<p>✅ Children query successful - found ' . count($children) . ' children</p>';
            } else {
                echo '<p>⚠️ parent_id column does not exist</p>';
            }
        } catch (Exception $e) {
            echo '<p>❌ Children query error: ' . $e->getMessage() . '</p>';
        }
        
        echo '<h3>4. Testing IP Stats Query</h3>';
        try {
            $ip_stats = (object)['total' => 0, 'assigned' => 0];
            if (Capsule::schema()->hasTable('dcim_ip_addresses')) {
                $ip_stats = Capsule::table('dcim_ip_addresses')
                    ->selectRaw('COUNT(*) as total, SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned')
                    ->where('subnet_id', $test_subnet->id)
                    ->first();
                echo '<p>✅ IP stats query successful</p>';
                echo '<pre>' . print_r($ip_stats, true) . '</pre>';
            } else {
                echo '<p>⚠️ dcim_ip_addresses table does not exist</p>';
            }
        } catch (Exception $e) {
            echo '<p>❌ IP stats query error: ' . $e->getMessage() . '</p>';
        }
        
        echo '<h3>5. Testing Complete AJAX Response</h3>';
        try {
            // Capture the AJAX output
            ob_start();
            dcim_handle_subnet_details_ajax($test_subnet->id);
            $ajax_output = ob_get_clean();
            
            echo '<h4>Raw AJAX Output:</h4>';
            echo '<pre>' . htmlspecialchars($ajax_output) . '</pre>';
            
            // Test JSON parsing
            $response = json_decode($ajax_output, true);
            if ($response) {
                echo '<h4>✅ JSON Parsed Successfully:</h4>';
                echo '<pre>' . print_r($response, true) . '</pre>';
            } else {
                echo '<h4>❌ JSON Parse Failed:</h4>';
                echo '<p>JSON Error: ' . json_last_error_msg() . '</p>';
                echo '<p>JSON Error Code: ' . json_last_error() . '</p>';
                
                // Check for non-printable characters
                echo '<h4>Character Analysis:</h4>';
                for ($i = 0; $i < strlen($ajax_output); $i++) {
                    $char = $ajax_output[$i];
                    $ord = ord($char);
                    if ($ord < 32 || $ord > 126) {
                        echo '<p>Non-printable character at position ' . $i . ': ASCII ' . $ord . '</p>';
                    }
                }
            }
        } catch (Exception $e) {
            echo '<p>❌ AJAX function error: ' . $e->getMessage() . '</p>';
        }
        
    } else {
        echo '<p>No subnets found. Please create a subnet first.</p>';
    }
    
} catch (Exception $e) {
    echo '<h2>❌ Fatal Error</h2>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

echo '<h2>Database Connection Test</h2>';
try {
    $result = Capsule::select('SELECT 1 as test');
    echo '<p>✅ Database connection successful</p>';
} catch (Exception $e) {
    echo '<p>❌ Database connection failed: ' . $e->getMessage() . '</p>';
}

?>
