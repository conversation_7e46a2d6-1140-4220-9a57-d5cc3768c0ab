<?php
/**
 * Test the subnet AJAX endpoint directly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include WHMCS bootstrap
require_once '../../../init.php';

use WHMCS\Database\Capsule;

// Include DCIM files
require_once 'dcim-core.php';
require_once 'dcim-ipam.php';

// If this is an AJAX request, handle it
if (isset($_GET['action']) && $_GET['action'] === 'ipam' && isset($_GET['subaction']) && $_GET['subaction'] === 'subnet_details') {
    dcim_handle_subnet_details_ajax($_GET['subnet_id']);
    exit;
}

echo '<!DOCTYPE html>
<html>
<head>
    <title>Test Subnet AJAX</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>';

echo '<h1>Test Subnet AJAX Endpoint</h1>';

try {
    // Get existing subnets
    $subnets = Capsule::table('dcim_subnets')->get();
    
    if (count($subnets) > 0) {
        echo '<div class="test-section">';
        echo '<h2>Available Subnets</h2>';
        foreach ($subnets as $subnet) {
            echo '<p>ID: ' . $subnet->id . ' - ' . htmlspecialchars($subnet->subnet) . ' 
                  <button onclick="testSubnet(' . $subnet->id . ')">Test AJAX</button></p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h2>Test Results</h2>';
        echo '<div id="results"></div>';
        echo '</div>';
        
        // Get the module link (simulate it)
        $modulelink = '/modules/addons/dcim/test-subnet-ajax.php?';
        
        echo '<script>
        function testSubnet(subnetId) {
            const resultsDiv = document.getElementById("results");
            resultsDiv.innerHTML = "<p class=\"info\">Testing subnet ID: " + subnetId + "...</p>";
            
            const url = "' . $modulelink . 'action=ipam&subaction=subnet_details&subnet_id=" + subnetId;
            console.log("Testing URL:", url);
            
            fetch(url, {
                method: "GET",
                headers: {
                    "Accept": "application/json",
                    "X-Requested-With": "XMLHttpRequest"
                },
                credentials: "same-origin"
            })
            .then(response => {
                console.log("Response status:", response.status);
                console.log("Response headers:", response.headers);
                
                if (!response.ok) {
                    throw new Error("HTTP " + response.status + ": " + response.statusText);
                }
                
                const contentType = response.headers.get("content-type");
                console.log("Content-Type:", contentType);
                
                return response.text();
            })
            .then(text => {
                console.log("Raw response:", text);
                
                let html = "<h3>Raw Response:</h3><pre>" + text.replace(/</g, "&lt;").replace(/>/g, "&gt;") + "</pre>";
                
                try {
                    const data = JSON.parse(text);
                    html += "<h3 class=\"success\">✅ JSON Parse Successful</h3>";
                    html += "<pre>" + JSON.stringify(data, null, 2) + "</pre>";
                    
                    if (data.success) {
                        html += "<p class=\"success\">✅ AJAX call successful!</p>";
                    } else {
                        html += "<p class=\"error\">❌ AJAX call failed: " + (data.error || "Unknown error") + "</p>";
                    }
                } catch (jsonError) {
                    html += "<h3 class=\"error\">❌ JSON Parse Failed</h3>";
                    html += "<p class=\"error\">Error: " + jsonError.message + "</p>";
                    
                    // Check for common issues
                    if (text.includes("<?php") || text.includes("<!DOCTYPE")) {
                        html += "<p class=\"error\">⚠️ Response contains HTML/PHP instead of JSON</p>";
                    }
                    if (text.includes("Warning:") || text.includes("Notice:") || text.includes("Error:")) {
                        html += "<p class=\"error\">⚠️ Response contains PHP errors/warnings</p>";
                    }
                    if (text.trim() === "") {
                        html += "<p class=\"error\">⚠️ Response is empty</p>";
                    }
                }
                
                resultsDiv.innerHTML = html;
            })
            .catch(error => {
                console.error("Fetch error:", error);
                resultsDiv.innerHTML = "<h3 class=\"error\">❌ Fetch Error</h3><p class=\"error\">" + error.message + "</p>";
            });
        }
        </script>';
        
    } else {
        echo '<div class="test-section">';
        echo '<p class="error">No subnets found. Please create a subnet first.</p>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="test-section">';
    echo '<h2 class="error">❌ Error</h2>';
    echo '<p class="error">' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
    echo '</div>';
}

echo '</body></html>';
?>
