<?php
/**
 * Test the actual AJAX response that's causing the JSON parsing error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include WHMCS bootstrap
require_once '../../../init.php';

use WHMCS\Database\Capsule;

// Include DCIM files
require_once 'dcim-core.php';
require_once 'dcim-ipam.php';

// Simulate the AJAX request
if (isset($_GET['test_subnet_id'])) {
    // This simulates the actual AJAX call
    $_GET['subnet_id'] = $_GET['test_subnet_id'];
    dcim_handle_subnet_details_ajax($_GET['test_subnet_id']);
    exit;
}

echo '<h1>Test AJAX Response</h1>';

try {
    // Get existing subnets
    $subnets = Capsule::table('dcim_subnets')->get();
    
    if (count($subnets) > 0) {
        $test_subnet = $subnets[0];
        echo '<h2>Testing Subnet ID: ' . $test_subnet->id . '</h2>';
        echo '<p>Subnet: ' . htmlspecialchars($test_subnet->subnet ?? 'NULL') . '</p>';
        
        // Create a link to test the AJAX response
        $test_url = $_SERVER['PHP_SELF'] . '?test_subnet_id=' . $test_subnet->id;
        echo '<p><a href="' . $test_url . '" target="_blank">Click here to see raw AJAX response</a></p>';
        
        // Also test with JavaScript fetch
        echo '<h3>Test with JavaScript Fetch</h3>';
        echo '<button onclick="testAjax()">Test AJAX Call</button>';
        echo '<div id="result"></div>';
        
        echo '<script>
        function testAjax() {
            const resultDiv = document.getElementById("result");
            resultDiv.innerHTML = "Loading...";
            
            fetch("' . $test_url . '")
                .then(response => {
                    console.log("Response status:", response.status);
                    console.log("Response headers:", response.headers);
                    return response.text(); // Get as text first
                })
                .then(text => {
                    console.log("Raw response:", text);
                    resultDiv.innerHTML = "<h4>Raw Response:</h4><pre>" + text.replace(/</g, "&lt;").replace(/>/g, "&gt;") + "</pre>";
                    
                    // Try to parse as JSON
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML += "<h4>✅ JSON Parsed Successfully:</h4><pre>" + JSON.stringify(data, null, 2) + "</pre>";
                    } catch (e) {
                        resultDiv.innerHTML += "<h4>❌ JSON Parse Error:</h4><p>" + e.message + "</p>";
                        
                        // Check for common issues
                        if (text.includes("<?php") || text.includes("<!DOCTYPE")) {
                            resultDiv.innerHTML += "<p>⚠️ Response contains HTML/PHP code instead of JSON</p>";
                        }
                        if (text.includes("Warning:") || text.includes("Notice:") || text.includes("Error:")) {
                            resultDiv.innerHTML += "<p>⚠️ Response contains PHP errors/warnings</p>";
                        }
                        if (text.trim() === "") {
                            resultDiv.innerHTML += "<p>⚠️ Response is empty</p>";
                        }
                    }
                })
                .catch(error => {
                    console.error("Fetch error:", error);
                    resultDiv.innerHTML = "<h4>❌ Fetch Error:</h4><p>" + error.message + "</p>";
                });
        }
        </script>';
        
        // Test the function directly
        echo '<h3>Direct Function Test</h3>';
        echo '<p>Testing dcim_handle_subnet_details_ajax() directly:</p>';
        
        // Capture any output before the function
        ob_start();
        $error_before = error_get_last();
        
        try {
            // Test the function
            dcim_handle_subnet_details_ajax($test_subnet->id);
        } catch (Exception $e) {
            echo '<p>❌ Exception: ' . $e->getMessage() . '</p>';
        }
        
        $output = ob_get_clean();
        $error_after = error_get_last();
        
        echo '<h4>Function Output:</h4>';
        echo '<pre>' . htmlspecialchars($output) . '</pre>';
        
        if ($error_after && $error_after !== $error_before) {
            echo '<h4>PHP Errors:</h4>';
            echo '<p>' . htmlspecialchars($error_after['message']) . '</p>';
        }
        
        // Test JSON validity
        $json_data = json_decode($output, true);
        if ($json_data) {
            echo '<h4>✅ JSON is valid</h4>';
            echo '<pre>' . print_r($json_data, true) . '</pre>';
        } else {
            echo '<h4>❌ JSON is invalid</h4>';
            echo '<p>JSON Error: ' . json_last_error_msg() . '</p>';
        }
        
    } else {
        echo '<p>No subnets found. Please create a subnet first.</p>';
        
        // Show how to create a test subnet
        echo '<h3>Create Test Subnet</h3>';
        echo '<form method="post">';
        echo '<p>Subnet (CIDR): <input type="text" name="subnet" value="192.168.1.0/24" required></p>';
        echo '<p>Subnet Type: <select name="subnet_type">';
        echo '<option value="Root">Root</option>';
        echo '<option value="Customer">Customer</option>';
        echo '<option value="Management">Management</option>';
        echo '<option value="Transit">Transit</option>';
        echo '</select></p>';
        echo '<p><input type="submit" name="create_test" value="Create Test Subnet"></p>';
        echo '</form>';
        
        if (isset($_POST['create_test'])) {
            try {
                $result = dcim_create_subnet($_POST);
                if ($result['success']) {
                    echo '<p>✅ Test subnet created successfully! <a href="' . $_SERVER['PHP_SELF'] . '">Refresh page</a></p>';
                } else {
                    echo '<p>❌ Error creating subnet: ' . htmlspecialchars($result['error']) . '</p>';
                }
            } catch (Exception $e) {
                echo '<p>❌ Exception creating subnet: ' . $e->getMessage() . '</p>';
            }
        }
    }
    
} catch (Exception $e) {
    echo '<h2>❌ Error</h2>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

?>
